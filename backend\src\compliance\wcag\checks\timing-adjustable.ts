/**
 * WCAG-044: Timing Adjustable Check
 * Success Criterion: 2.2.1 Timing Adjustable (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckTemplate, CheckConfig, CheckFunction } from '../utils/check-template';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceProcessor } from '../utils/evidence-processor';

export class TimingAdjustableCheck {
  private checkTemplate = new CheckTemplate();

  async performCheck(config: CheckConfig): Promise<WcagCheckResultEnhanced> {
    const result = await this.checkTemplate.executeCheck(
      'WCAG-044',
      'Timing Adjustable',
      'operable',
      0.0687,
      'A',
      config,
      this.executeTimingAdjustableCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Convert to enhanced result
    return {
      ...result,
      evidence: EvidenceProcessor.processEvidence(result.evidence),
    };
  }

  private async executeTimingAdjustableCheck(
    page: Page,
    config: CheckConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Check for timing-related elements and scripts
    const timingAnalysis = await page.evaluate(() => {
      const timingElements: Array<{
        type: string;
        element: string;
        selector: string;
        hasControls: boolean;
        timeValue?: string;
        description: string;
      }> = [];

      // Check for session timeout warnings
      const sessionElements = document.querySelectorAll(
        '[data-timeout], [data-session], .timeout, .session-warning, #timeout, #session'
      );
      
      sessionElements.forEach((element, index) => {
        const hasControls = element.querySelector('button, input, a, [role="button"]') !== null;
        timingElements.push({
          type: 'session_timeout',
          element: element.tagName.toLowerCase(),
          selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          hasControls,
          description: 'Session timeout element detected',
        });
      });

      // Check for auto-refresh meta tags
      const metaRefresh = document.querySelectorAll('meta[http-equiv="refresh"]');
      metaRefresh.forEach((meta, index) => {
        const content = meta.getAttribute('content') || '';
        const timeMatch = content.match(/^(\d+)/);
        const timeValue = timeMatch ? timeMatch[1] : '';
        
        timingElements.push({
          type: 'meta_refresh',
          element: 'meta',
          selector: `meta[http-equiv="refresh"]:nth-of-type(${index + 1})`,
          hasControls: false,
          timeValue,
          description: `Auto-refresh meta tag with ${timeValue} second delay`,
        });
      });

      // Check for JavaScript timers (common patterns)
      const scripts = document.querySelectorAll('script');
      let hasTimerCode = false;
      
      scripts.forEach((script) => {
        const scriptContent = script.textContent || '';
        if (scriptContent.includes('setTimeout') || 
            scriptContent.includes('setInterval') ||
            scriptContent.includes('location.reload') ||
            scriptContent.includes('window.location.href')) {
          hasTimerCode = true;
        }
      });

      if (hasTimerCode) {
        timingElements.push({
          type: 'javascript_timer',
          element: 'script',
          selector: 'script',
          hasControls: false,
          description: 'JavaScript timer or redirect code detected',
        });
      }

      // Check for countdown timers
      const countdownElements = document.querySelectorAll(
        '.countdown, .timer, [data-countdown], [data-timer], #countdown, #timer'
      );
      
      countdownElements.forEach((element, index) => {
        const hasControls = element.querySelector('button, input, a, [role="button"]') !== null;
        timingElements.push({
          type: 'countdown_timer',
          element: element.tagName.toLowerCase(),
          selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          hasControls,
          description: 'Countdown timer element detected',
        });
      });

      // Check for form timeout warnings
      const formTimeouts = document.querySelectorAll('form [data-timeout], form .timeout');
      formTimeouts.forEach((element, index) => {
        const hasControls = element.querySelector('button, input, a, [role="button"]') !== null;
        timingElements.push({
          type: 'form_timeout',
          element: element.tagName.toLowerCase(),
          selector: `form ${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          hasControls,
          description: 'Form timeout warning detected',
        });
      });

      return {
        timingElements,
        totalElements: timingElements.length,
        elementsWithControls: timingElements.filter(el => el.hasControls).length,
      };
    });

    let score = 100;
    const elementCount = timingAnalysis.totalElements;
    const scanDuration = Date.now() - startTime;

    if (elementCount > 0) {
      const elementsWithoutControls = elementCount - timingAnalysis.elementsWithControls;
      
      if (elementsWithoutControls > 0) {
        score = Math.max(0, 100 - (elementsWithoutControls * 25)); // Deduct 25 points per element without controls
        issues.push(`${elementsWithoutControls} timing elements found without user controls`);
        
        timingAnalysis.timingElements
          .filter(el => !el.hasControls)
          .forEach((element) => {
            evidence.push({
              type: 'code',
              description: `Timing element without user controls: ${element.description}`,
              value: element.timeValue ? 
                `Time limit: ${element.timeValue} seconds` : 
                'Timing element detected',
              selector: element.selector,
              elementCount: 1,
              affectedSelectors: [element.selector],
              severity: 'error',
              fixExample: {
                before: this.getBeforeExample(element.type),
                after: this.getAfterExample(element.type),
                description: this.getFixDescription(element.type),
                codeExample: this.getCodeExample(element.type),
                resources: [
                  'https://www.w3.org/WAI/WCAG21/Understanding/timing-adjustable.html',
                  'https://www.w3.org/WAI/WCAG21/Techniques/G133',
                  'https://www.w3.org/WAI/WCAG21/Techniques/G180'
                ]
              },
              metadata: {
                scanDuration,
                elementsAnalyzed: 1,
                checkSpecificData: {
                  timingType: element.type,
                  hasControls: element.hasControls,
                  timeValue: element.timeValue || 'unknown',
                },
              },
            });
          });
        
        recommendations.push('Provide user controls to turn off, adjust, or extend time limits');
        recommendations.push('Allow users to extend time limits by at least 10 times the default');
        recommendations.push('Warn users before time expires and provide at least 20 seconds to extend');
        recommendations.push('Consider removing automatic time limits where possible');
      }
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(type: string): string {
    switch (type) {
      case 'meta_refresh':
        return '<meta http-equiv="refresh" content="30; url=next-page.html">';
      case 'session_timeout':
        return '<div class="timeout">Session will expire in 5 minutes</div>';
      case 'countdown_timer':
        return '<div class="countdown">Time remaining: 00:05:00</div>';
      case 'form_timeout':
        return '<div class="timeout">Form will timeout in 10 minutes</div>';
      default:
        return 'Timing element without controls';
    }
  }

  private getAfterExample(type: string): string {
    switch (type) {
      case 'meta_refresh':
        return '<!-- Remove auto-refresh or provide user control -->\n<button onclick="refreshPage()">Refresh Page</button>';
      case 'session_timeout':
        return '<div class="timeout">Session will expire in 5 minutes\n  <button onclick="extendSession()">Extend Session</button>\n</div>';
      case 'countdown_timer':
        return '<div class="countdown">Time remaining: 00:05:00\n  <button onclick="pauseTimer()">Pause</button>\n  <button onclick="extendTimer()">Extend Time</button>\n</div>';
      case 'form_timeout':
        return '<div class="timeout">Form will timeout in 10 minutes\n  <button onclick="extendTimeout()">Extend Time</button>\n</div>';
      default:
        return 'Timing element with user controls';
    }
  }

  private getFixDescription(type: string): string {
    switch (type) {
      case 'meta_refresh':
        return 'Remove automatic refresh or provide user control to refresh';
      case 'session_timeout':
        return 'Add controls to extend or disable session timeout';
      case 'countdown_timer':
        return 'Provide controls to pause, stop, or extend countdown timers';
      case 'form_timeout':
        return 'Add controls to extend form timeout periods';
      default:
        return 'Provide user controls for timing elements';
    }
  }

  private getCodeExample(type: string): string {
    switch (type) {
      case 'meta_refresh':
        return `
<!-- Before: Automatic refresh -->
<meta http-equiv="refresh" content="30; url=next-page.html">

<!-- After: User-controlled refresh -->
<script>
function refreshPage() {
  if (confirm('Refresh the page?')) {
    window.location.reload();
  }
}
</script>
<button onclick="refreshPage()">Refresh Page</button>
        `;
      case 'session_timeout':
        return `
<!-- Before: No user control -->
<div class="timeout">Session expires in 5 minutes</div>

<!-- After: With user controls -->
<div class="timeout">
  Session expires in <span id="timer">5:00</span>
  <button onclick="extendSession()">Extend Session</button>
  <button onclick="disableTimeout()">Disable Timeout</button>
</div>
        `;
      case 'countdown_timer':
        return `
<!-- Before: No user control -->
<div class="countdown">Time remaining: 00:05:00</div>

<!-- After: With user controls -->
<div class="countdown">
  Time remaining: <span id="countdown">00:05:00</span>
  <button onclick="pauseTimer()">Pause</button>
  <button onclick="extendTimer()">Extend Time</button>
  <button onclick="stopTimer()">Stop Timer</button>
</div>
        `;
      case 'form_timeout':
        return `
<!-- Before: No user control -->
<div class="timeout">Form will timeout in 10 minutes</div>

<!-- After: With user controls -->
<div class="timeout">
  Form will timeout in <span id="form-timer">10:00</span>
  <button onclick="extendTimeout()">Extend Time</button>
  <button onclick="saveAndContinue()">Save & Continue Later</button>
</div>
        `;
      default:
        return 'Provide appropriate user controls for timing elements';
    }
  }
}

/**
 * WCAG-048: Enhanced Focus Management Check
 * Success Criterion: 2.4.3 Focus Order (Level A)
 * Enhanced version with comprehensive focus order validation
 */

import { Page } from 'puppeteer';
import { CheckTemplate, CheckConfig } from '../utils/check-template';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceProcessor } from '../utils/evidence-processor';

export class EnhancedFocusManagementCheck {
  private checkTemplate = new CheckTemplate();

  async performCheck(config: CheckConfig): Promise<WcagCheckResultEnhanced> {
    const result = await this.checkTemplate.executeCheck(
      'WCAG-048',
      'Enhanced Focus Management',
      'operable',
      0.0611,
      'A',
      config,
      this.executeEnhancedFocusManagementCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Convert to enhanced result
    return {
      ...result,
      evidence: EvidenceProcessor.processEvidence(result.evidence),
    };
  }

  private async executeEnhancedFocusManagementCheck(
    page: Page,
    _config: CheckConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Enhanced focus order analysis
    const focusAnalysis = await page.evaluate(() => {
      const focusableElements: Array<{
        tagName: string;
        selector: string;
        tabIndex: number;
        isVisible: boolean;
        hasTabIndex: boolean;
        isInTabOrder: boolean;
        visualOrder: number;
        logicalOrder: number;
        ariaHidden: boolean;
        disabled: boolean;
        type?: string;
        role?: string;
      }> = [];

      // Get all potentially focusable elements
      const focusableSelectors = [
        'a[href]', 'button', 'input', 'textarea', 'select',
        '[tabindex]', '[contenteditable="true"]', 'iframe',
        'audio[controls]', 'video[controls]', 'details summary'
      ];

      const allElements = document.querySelectorAll(focusableSelectors.join(', '));
      
      allElements.forEach((element, index) => {
        const computedStyle = window.getComputedStyle(element);
        const rect = element.getBoundingClientRect();
        
        const isVisible = rect.width > 0 && rect.height > 0 &&
                         computedStyle.visibility !== 'hidden' &&
                         computedStyle.display !== 'none' &&
                         computedStyle.opacity !== '0';
        
        const tabIndex = parseInt(element.getAttribute('tabindex') || '0');
        const hasTabIndex = element.hasAttribute('tabindex');
        const ariaHidden = element.getAttribute('aria-hidden') === 'true';
        const disabled = (element as HTMLInputElement).disabled || 
                        element.getAttribute('aria-disabled') === 'true';
        
        // Determine if element is in tab order
        const isInTabOrder = !disabled && !ariaHidden && 
                           (tabIndex >= 0 || (!hasTabIndex && isVisible));
        
        // Calculate visual order (top-to-bottom, left-to-right)
        const visualOrder = Math.round(rect.top) * 10000 + Math.round(rect.left);
        
        focusableElements.push({
          tagName: element.tagName.toLowerCase(),
          selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          tabIndex,
          isVisible,
          hasTabIndex,
          isInTabOrder,
          visualOrder,
          logicalOrder: index,
          ariaHidden,
          disabled,
          type: (element as HTMLInputElement).type,
          role: element.getAttribute('role') || undefined,
        });
      });

      // Sort by tab order (tabindex > 0 first, then document order)
      const tabOrderElements = focusableElements
        .filter(el => el.isInTabOrder)
        .sort((a, b) => {
          if (a.tabIndex > 0 && b.tabIndex > 0) return a.tabIndex - b.tabIndex;
          if (a.tabIndex > 0) return -1;
          if (b.tabIndex > 0) return 1;
          return a.logicalOrder - b.logicalOrder;
        });

      // Sort by visual order
      const visualOrderElements = [...tabOrderElements].sort((a, b) => a.visualOrder - b.visualOrder);

      // Find focus order issues
      const focusOrderIssues: Array<{
        type: string;
        description: string;
        elements: typeof focusableElements;
        severity: 'error' | 'warning' | 'info';
      }> = [];

      // Check for positive tabindex values
      const positiveTabIndexElements = focusableElements.filter(el => el.tabIndex > 0);
      if (positiveTabIndexElements.length > 0) {
        focusOrderIssues.push({
          type: 'positive_tabindex',
          description: `${positiveTabIndexElements.length} elements with positive tabindex values`,
          elements: positiveTabIndexElements,
          severity: 'warning',
        });
      }

      // Check for significant visual/logical order mismatches
      const orderMismatches = [];
      for (let i = 0; i < Math.min(tabOrderElements.length, visualOrderElements.length); i++) {
        const tabElement = tabOrderElements[i];
        const visualElement = visualOrderElements[i];
        
        if (tabElement.selector !== visualElement.selector) {
          const tabPosition = tabOrderElements.findIndex(el => el.selector === visualElement.selector);
          if (Math.abs(i - tabPosition) > 2) { // Significant mismatch
            orderMismatches.push({
              visual: visualElement,
              tab: tabElement,
              visualIndex: i,
              tabIndex: tabPosition,
            });
          }
        }
      }

      if (orderMismatches.length > 0) {
        focusOrderIssues.push({
          type: 'order_mismatch',
          description: `${orderMismatches.length} significant visual/tab order mismatches`,
          elements: orderMismatches.map(m => m.visual),
          severity: 'error',
        });
      }

      // Check for invisible focusable elements
      const invisibleFocusable = focusableElements.filter(el => el.isInTabOrder && !el.isVisible);
      if (invisibleFocusable.length > 0) {
        focusOrderIssues.push({
          type: 'invisible_focusable',
          description: `${invisibleFocusable.length} invisible elements in tab order`,
          elements: invisibleFocusable,
          severity: 'error',
        });
      }

      // Check for focus traps (elements with very high tabindex)
      const highTabIndex = focusableElements.filter(el => el.tabIndex > 100);
      if (highTabIndex.length > 0) {
        focusOrderIssues.push({
          type: 'high_tabindex',
          description: `${highTabIndex.length} elements with very high tabindex values`,
          elements: highTabIndex,
          severity: 'warning',
        });
      }

      return {
        focusableElements,
        tabOrderElements,
        visualOrderElements,
        focusOrderIssues,
        totalFocusable: focusableElements.length,
        inTabOrder: tabOrderElements.length,
        positiveTabIndex: positiveTabIndexElements.length,
        orderMismatches: orderMismatches.length,
      };
    });

    let score = 100;
    const elementCount = focusAnalysis.totalFocusable;
    const scanDuration = Date.now() - startTime;

    // Evaluate focus order issues
    focusAnalysis.focusOrderIssues.forEach((issue) => {
      const deduction = issue.severity === 'error' ? 25 : 
                       issue.severity === 'warning' ? 15 : 5;
      score = Math.max(0, score - (issue.elements.length * deduction));
      issues.push(issue.description);

      evidence.push({
        type: 'code',
        description: `Focus order issue: ${issue.description}`,
        value: `${issue.elements.length} elements affected`,
        selector: issue.elements.map(el => el.selector).join(', '),
        elementCount: issue.elements.length,
        affectedSelectors: issue.elements.map(el => el.selector),
        severity: issue.severity,
        fixExample: {
          before: this.getBeforeExample(issue.type),
          after: this.getAfterExample(issue.type),
          description: this.getFixDescription(issue.type),
          codeExample: this.getCodeExample(issue.type),
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/focus-order.html',
            'https://www.w3.org/WAI/WCAG21/Techniques/G59',
            'https://www.w3.org/WAI/WCAG21/Techniques/H4'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: issue.elements.length,
          checkSpecificData: {
            issueType: issue.type,
            totalFocusable: focusAnalysis.totalFocusable,
            inTabOrder: focusAnalysis.inTabOrder,
            positiveTabIndex: focusAnalysis.positiveTabIndex,
          },
        },
      });
    });

    // Add recommendations
    if (focusAnalysis.positiveTabIndex > 0) {
      recommendations.push('Avoid positive tabindex values; use document order instead');
    }
    if (focusAnalysis.orderMismatches > 0) {
      recommendations.push('Ensure tab order matches visual order');
    }
    recommendations.push('Test focus order with keyboard navigation');
    recommendations.push('Ensure all interactive elements are keyboard accessible');

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(type: string): string {
    switch (type) {
      case 'positive_tabindex':
        return '<button tabindex="5">Button 1</button>\n<button tabindex="1">Button 2</button>';
      case 'order_mismatch':
        return '<div style="float: right;"><button>Right Button</button></div>\n<button>Left Button</button>';
      case 'invisible_focusable':
        return '<button style="display: none;">Hidden Button</button>';
      case 'high_tabindex':
        return '<button tabindex="999">High Priority Button</button>';
      default:
        return 'Focus order issue';
    }
  }

  private getAfterExample(type: string): string {
    switch (type) {
      case 'positive_tabindex':
        return '<button>Button 1</button>\n<button>Button 2</button>';
      case 'order_mismatch':
        return '<button>Left Button</button>\n<div style="float: right;"><button tabindex="0">Right Button</button></div>';
      case 'invisible_focusable':
        return '<button>Visible Button</button>';
      case 'high_tabindex':
        return '<button>Normal Priority Button</button>';
      default:
        return 'Fixed focus order';
    }
  }

  private getFixDescription(type: string): string {
    switch (type) {
      case 'positive_tabindex':
        return 'Remove positive tabindex values and use document order';
      case 'order_mismatch':
        return 'Reorder elements to match visual layout';
      case 'invisible_focusable':
        return 'Remove invisible elements from tab order';
      case 'high_tabindex':
        return 'Use normal tabindex values (0 or -1)';
      default:
        return 'Fix focus order implementation';
    }
  }

  private getCodeExample(type: string): string {
    switch (type) {
      case 'positive_tabindex':
        return `
<!-- Before: Positive tabindex disrupts natural order -->
<button tabindex="5">Last in tab order</button>
<button tabindex="1">First in tab order</button>
<button>Second in tab order</button>

<!-- After: Natural document order -->
<button>First in tab order</button>
<button>Second in tab order</button>
<button>Third in tab order</button>
        `;
      case 'order_mismatch':
        return `
<!-- Before: Visual order doesn't match tab order -->
<div class="container">
  <div style="float: right;">
    <button>Visually first</button>
  </div>
  <button>Visually second</button>
</div>

<!-- After: Tab order matches visual order -->
<div class="container">
  <button>First button</button>
  <div style="float: right;">
    <button>Second button</button>
  </div>
</div>
        `;
      default:
        return 'Ensure logical focus order that matches visual layout';
    }
  }
}

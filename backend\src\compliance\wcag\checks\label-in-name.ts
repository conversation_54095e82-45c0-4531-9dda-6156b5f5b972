/**
 * WCAG-055: Label in Name Check
 * Success Criterion: 2.5.3 Label in Name (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckTemplate, CheckConfig } from '../utils/check-template';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceProcessor } from '../utils/evidence-processor';

export class LabelInNameCheck {
  private checkTemplate = new CheckTemplate();

  async performCheck(config: CheckConfig): Promise<WcagCheckResultEnhanced> {
    const result = await this.checkTemplate.executeCheck(
      'WCAG-055',
      'Label in Name',
      'operable',
      0.0458,
      'A',
      config,
      this.executeLabelInNameCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Convert to enhanced result
    return {
      ...result,
      evidence: EvidenceProcessor.processEvidence(result.evidence),
    };
  }

  private async executeLabelInNameCheck(
    page: Page,
    _config: CheckConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze label and accessible name consistency
    const labelAnalysis = await page.evaluate(() => {
      const problematicElements: Array<{
        selector: string;
        tagName: string;
        visibleText: string;
        accessibleName: string;
        labelText: string;
        ariaLabel: string;
        hasMatch: boolean;
        matchType: string;
        issues: string[];
        severity: 'error' | 'warning' | 'info';
      }> = [];

      // Helper function to normalize text for comparison
      function normalizeText(text: string): string {
        return text.toLowerCase()
                  .replace(/[^\w\s]/g, '') // Remove punctuation
                  .replace(/\s+/g, ' ')    // Normalize whitespace
                  .trim();
      }

      // Helper function to check if visible text is contained in accessible name
      function checkTextMatch(visibleText: string, accessibleName: string): { hasMatch: boolean; matchType: string } {
        const normalizedVisible = normalizeText(visibleText);
        const normalizedAccessible = normalizeText(accessibleName);
        
        if (normalizedVisible === normalizedAccessible) {
          return { hasMatch: true, matchType: 'exact' };
        }
        
        if (normalizedAccessible.includes(normalizedVisible)) {
          return { hasMatch: true, matchType: 'contained' };
        }
        
        // Check for partial word matches
        const visibleWords = normalizedVisible.split(' ');
        const accessibleWords = normalizedAccessible.split(' ');
        
        const matchingWords = visibleWords.filter(word => 
          word.length > 2 && accessibleWords.some(accWord => accWord.includes(word))
        );
        
        if (matchingWords.length >= Math.ceil(visibleWords.length * 0.5)) {
          return { hasMatch: true, matchType: 'partial' };
        }
        
        return { hasMatch: false, matchType: 'none' };
      }

      // Get all interactive elements with visible text
      const interactiveElements = document.querySelectorAll(`
        button, input[type="button"], input[type="submit"], input[type="reset"],
        a[href], [role="button"], [role="link"], [role="menuitem"], [role="tab"],
        [onclick], [tabindex]:not([tabindex="-1"])
      `);

      interactiveElements.forEach((element, index) => {
        const visibleText = element.textContent?.trim() || '';
        
        // Skip elements without visible text
        if (visibleText.length === 0) return;
        
        // Get accessible name
        const ariaLabel = element.getAttribute('aria-label') || '';
        const ariaLabelledBy = element.getAttribute('aria-labelledby');
        const title = element.getAttribute('title') || '';
        
        let accessibleName = '';
        let labelText = '';
        
        // Determine accessible name source
        if (ariaLabel) {
          accessibleName = ariaLabel;
        } else if (ariaLabelledBy) {
          const labelElement = document.getElementById(ariaLabelledBy);
          if (labelElement) {
            accessibleName = labelElement.textContent?.trim() || '';
            labelText = accessibleName;
          }
        } else if (element.tagName === 'INPUT') {
          // For inputs, check associated label
          const input = element as HTMLInputElement;
          const label = document.querySelector(`label[for="${input.id}"]`) as HTMLLabelElement;
          if (label) {
            accessibleName = label.textContent?.trim() || '';
            labelText = accessibleName;
          } else {
            // Check if input is inside a label
            const parentLabel = input.closest('label');
            if (parentLabel) {
              accessibleName = parentLabel.textContent?.trim() || '';
              labelText = accessibleName;
            }
          }
        } else if (title) {
          accessibleName = title;
        } else {
          accessibleName = visibleText; // Fallback to visible text
        }
        
        // Skip if no accessible name
        if (!accessibleName) return;
        
        const { hasMatch, matchType } = checkTextMatch(visibleText, accessibleName);
        
        const issues: string[] = [];
        let severity: 'error' | 'warning' | 'info' = 'info';
        
        if (!hasMatch) {
          issues.push('Visible text not found in accessible name');
          severity = 'error';
        } else if (matchType === 'partial') {
          issues.push('Visible text only partially matches accessible name');
          severity = 'warning';
        }
        
        // Check for common problematic patterns
        if (ariaLabel && visibleText.length > 0 && !hasMatch) {
          issues.push('aria-label completely different from visible text');
          severity = 'error';
        }
        
        if (title && !ariaLabel && !hasMatch) {
          issues.push('title attribute used as accessible name but differs from visible text');
          severity = 'warning';
        }
        
        // Check for icon-only buttons with aria-label
        if (visibleText.length < 3 && ariaLabel.length > 0) {
          // This might be an icon button, which is acceptable
          severity = 'info';
          if (issues.length === 0) {
            issues.push('Icon button with descriptive aria-label (acceptable)');
          }
        }
        
        if (issues.length > 0 || !hasMatch) {
          problematicElements.push({
            selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
            tagName: element.tagName.toLowerCase(),
            visibleText: visibleText.substring(0, 50),
            accessibleName: accessibleName.substring(0, 50),
            labelText: labelText.substring(0, 50),
            ariaLabel: ariaLabel.substring(0, 50),
            hasMatch,
            matchType,
            issues,
            severity,
          });
        }
      });

      return {
        problematicElements,
        totalInteractiveElements: interactiveElements.length,
        problematicCount: problematicElements.length,
        noMatchCount: problematicElements.filter(el => !el.hasMatch).length,
        partialMatchCount: problematicElements.filter(el => el.matchType === 'partial').length,
        ariaLabelIssues: problematicElements.filter(el => 
          el.issues.some(issue => issue.includes('aria-label'))
        ).length,
      };
    });

    let score = 100;
    const elementCount = labelAnalysis.problematicCount;
    const scanDuration = Date.now() - startTime;

    if (elementCount > 0) {
      // Calculate score based on severity
      labelAnalysis.problematicElements.forEach((element) => {
        const deduction = element.severity === 'error' ? 15 : 
                         element.severity === 'warning' ? 8 : 2;
        score = Math.max(0, score - deduction);
      });

      issues.push(`${elementCount} elements with label/name consistency issues found`);
      if (labelAnalysis.noMatchCount > 0) {
        issues.push(`${labelAnalysis.noMatchCount} elements have completely different visible and accessible names`);
      }
      if (labelAnalysis.ariaLabelIssues > 0) {
        issues.push(`${labelAnalysis.ariaLabelIssues} elements have problematic aria-label usage`);
      }

      labelAnalysis.problematicElements.forEach((element) => {
        evidence.push({
          type: 'code',
          description: `Label in name issue: ${element.issues.join(', ')}`,
          value: `Visible: "${element.visibleText}" | Accessible: "${element.accessibleName}"`,
          selector: element.selector,
          elementCount: 1,
          affectedSelectors: [element.selector],
          severity: element.severity,
          fixExample: {
            before: this.getBeforeExample(element),
            after: this.getAfterExample(element),
            description: this.getFixDescription(element.issues),
            codeExample: this.getCodeExample(element.tagName),
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/label-in-name.html',
              'https://www.w3.org/WAI/WCAG21/Techniques/G208',
              'https://www.w3.org/WAI/WCAG21/Techniques/G211'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              visibleText: element.visibleText,
              accessibleName: element.accessibleName,
              hasMatch: element.hasMatch,
              matchType: element.matchType,
              ariaLabel: element.ariaLabel,
              issues: element.issues,
            },
          },
        });
      });
    }

    // Add recommendations
    recommendations.push('Ensure visible text is included in accessible names');
    recommendations.push('Start accessible names with the visible text when possible');
    recommendations.push('Avoid aria-labels that completely replace visible text');
    recommendations.push('Use consistent terminology between visible and accessible names');
    recommendations.push('Test with voice control software to verify name matching');

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(element: any): string {
    if (element.issues.includes('aria-label completely different')) {
      return `<button aria-label="${element.accessibleName}">${element.visibleText}</button>`;
    }
    if (element.issues.includes('title attribute')) {
      return `<button title="${element.accessibleName}">${element.visibleText}</button>`;
    }
    return `<${element.tagName} aria-label="${element.accessibleName}">${element.visibleText}</${element.tagName}>`;
  }

  private getAfterExample(element: any): string {
    if (element.issues.includes('aria-label completely different')) {
      return `<button aria-label="${element.visibleText} - additional context">${element.visibleText}</button>`;
    }
    if (element.issues.includes('title attribute')) {
      return `<button>${element.visibleText}</button>`;
    }
    return `<${element.tagName}>${element.visibleText}</${element.tagName}>`;
  }

  private getFixDescription(issues: string[]): string {
    if (issues.includes('not found in accessible name')) {
      return 'Include visible text in the accessible name';
    }
    if (issues.includes('aria-label completely different')) {
      return 'Start aria-label with visible text, then add context';
    }
    if (issues.includes('partially matches')) {
      return 'Ensure visible text appears at start of accessible name';
    }
    return 'Align visible text with accessible name';
  }

  private getCodeExample(tagName: string): string {
    switch (tagName) {
      case 'button':
        return `
<!-- Before: Visible text not in accessible name -->
<button aria-label="Close dialog">X</button>
<button aria-label="Navigate to next page">Continue</button>

<!-- After: Visible text included in accessible name -->
<button aria-label="Close dialog">Close</button>
<button aria-label="Continue to next page">Continue</button>

<!-- Alternative: Start with visible text -->
<button aria-label="X - Close dialog">X</button>
<button aria-label="Continue - Navigate to next page">Continue</button>
        `;
      case 'input':
        return `
<!-- Before: Label doesn't match button text -->
<label for="submit-btn">Send form</label>
<input type="submit" id="submit-btn" value="Submit">

<!-- After: Consistent labeling -->
<label for="submit-btn">Submit form</label>
<input type="submit" id="submit-btn" value="Submit">

<!-- Alternative: Use aria-label that includes visible text -->
<input type="submit" value="Submit" aria-label="Submit form">
        `;
      case 'a':
        return `
<!-- Before: Link text not in accessible name -->
<a href="/products" aria-label="View our product catalog">Shop</a>

<!-- After: Include link text in accessible name -->
<a href="/products" aria-label="Shop - View our product catalog">Shop</a>

<!-- Better: Use descriptive link text -->
<a href="/products">Shop Products</a>
        `;
      default:
        return `
<!-- General principle: Visible text should be in accessible name -->

<!-- Before: Mismatch between visible and accessible names -->
<div role="button" aria-label="Activate feature" onclick="toggle()">
  Enable
</div>

<!-- After: Accessible name includes visible text -->
<div role="button" aria-label="Enable feature" onclick="toggle()">
  Enable
</div>

<!-- Best practice: Use semantic HTML when possible -->
<button onclick="toggle()">Enable Feature</button>

<!-- For icon buttons, describe the action -->
<button aria-label="Save document">
  <svg><!-- save icon --></svg>
  Save
</button>

<!-- Voice control users can say "click save" -->
        `;
    }
  }
}

/**
 * WCAG-043: Content on Hover/Focus Check
 * Success Criterion: 1.4.13 Content on Hover or Focus (Level AA)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckTemplate, CheckConfig } from '../utils/check-template';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceProcessor } from '../utils/evidence-processor';

export class ContentOnHoverFocusCheck {
  private checkTemplate = new CheckTemplate();

  async performCheck(config: CheckConfig): Promise<WcagCheckResultEnhanced> {
    const result = await this.checkTemplate.executeCheck(
      'WCAG-043',
      'Content on Hover or Focus',
      'perceivable',
      0.0535,
      'AA',
      config,
      this.executeContentOnHoverFocusCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Convert to enhanced result
    return {
      ...result,
      evidence: EvidenceProcessor.processEvidence(result.evidence),
    };
  }

  private async executeContentOnHoverFocusCheck(
    page: Page,
    _config: CheckConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze hover/focus triggered content
    const hoverFocusAnalysis = await page.evaluate(() => {
      const problematicElements: Array<{
        selector: string;
        tagName: string;
        triggerType: string;
        contentType: string;
        isDismissible: boolean;
        isHoverable: boolean;
        isPersistent: boolean;
        hasEscapeKey: boolean;
        hasCloseButton: boolean;
        issues: string[];
        severity: 'error' | 'warning' | 'info';
      }> = [];

      // Common selectors for hover/focus content
      const hoverElements = document.querySelectorAll(`
        [title], [data-tooltip], .tooltip, .popover, .dropdown-menu,
        [aria-describedby], [aria-expanded], [data-toggle],
        .has-tooltip, .hint, .help-text, [data-popup]
      `);

      hoverElements.forEach((element, index) => {
        const issues: string[] = [];
        let severity: 'error' | 'warning' | 'info' = 'info';
        
        // Check for title attribute (basic tooltip)
        const title = element.getAttribute('title');
        if (title && title.trim().length > 0) {
          // Title tooltips are not dismissible, hoverable, or persistent
          issues.push('Title attribute tooltip not dismissible');
          issues.push('Title attribute tooltip not hoverable');
          issues.push('Title attribute tooltip not persistent');
          severity = 'error';
          
          problematicElements.push({
            selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
            tagName: element.tagName.toLowerCase(),
            triggerType: 'hover',
            contentType: 'title-tooltip',
            isDismissible: false,
            isHoverable: false,
            isPersistent: false,
            hasEscapeKey: false,
            hasCloseButton: false,
            issues: [...issues],
            severity,
          });
        }
        
        // Check for aria-describedby (custom tooltips)
        const describedBy = element.getAttribute('aria-describedby');
        if (describedBy) {
          const tooltipElement = document.getElementById(describedBy);
          if (tooltipElement) {
            const tooltipIssues: string[] = [];
            let tooltipSeverity: 'error' | 'warning' | 'info' = 'info';
            
            // Check if tooltip is dismissible (ESC key or click outside)
            const hasEscapeHandler = tooltipElement.hasAttribute('data-dismiss') ||
                                   tooltipElement.querySelector('[data-dismiss]') !== null;
            
            // Check if tooltip has close button
            const hasCloseButton = tooltipElement.querySelector('.close, .btn-close, [aria-label*="close"]') !== null;
            
            // Check if tooltip content is hoverable
            const tooltipStyle = window.getComputedStyle(tooltipElement);
            const isHoverable = tooltipStyle.pointerEvents !== 'none';
            
            // Check if tooltip persists on focus
            const isPersistent = element.matches(':focus') && 
                               tooltipStyle.display !== 'none' &&
                               tooltipStyle.visibility !== 'hidden';
            
            if (!hasEscapeHandler && !hasCloseButton) {
              tooltipIssues.push('Tooltip not dismissible with ESC or close button');
              tooltipSeverity = 'error';
            }
            
            if (!isHoverable) {
              tooltipIssues.push('Tooltip content not hoverable');
              tooltipSeverity = 'warning';
            }
            
            if (tooltipIssues.length > 0) {
              problematicElements.push({
                selector: `#${describedBy}`,
                tagName: tooltipElement.tagName.toLowerCase(),
                triggerType: 'hover-focus',
                contentType: 'custom-tooltip',
                isDismissible: hasEscapeHandler || hasCloseButton,
                isHoverable,
                isPersistent,
                hasEscapeKey: hasEscapeHandler,
                hasCloseButton,
                issues: tooltipIssues,
                severity: tooltipSeverity,
              });
            }
          }
        }
      });

      // Check for dropdown menus
      const dropdowns = document.querySelectorAll('.dropdown, [data-toggle="dropdown"], .has-dropdown');
      dropdowns.forEach((element, index) => {
        const menu = element.querySelector('.dropdown-menu, .menu, [role="menu"]');
        if (menu) {
          const issues: string[] = [];
          let severity: 'error' | 'warning' | 'info' = 'info';
          
          const menuStyle = window.getComputedStyle(menu);
          const isVisible = menuStyle.display !== 'none' && menuStyle.visibility !== 'hidden';
          
          if (isVisible) {
            // Check if menu is dismissible
            const hasEscapeHandler = menu.hasAttribute('data-dismiss') ||
                                   document.addEventListener('keydown', () => {}) !== undefined;
            
            // Check if menu is hoverable
            const isHoverable = menuStyle.pointerEvents !== 'none';
            
            if (!hasEscapeHandler) {
              issues.push('Dropdown menu not dismissible with ESC key');
              severity = 'warning';
            }
            
            if (!isHoverable) {
              issues.push('Dropdown menu content not hoverable');
              severity = 'warning';
            }
            
            if (issues.length > 0) {
              problematicElements.push({
                selector: `.dropdown:nth-of-type(${index + 1}) .dropdown-menu`,
                tagName: menu.tagName.toLowerCase(),
                triggerType: 'hover-click',
                contentType: 'dropdown-menu',
                isDismissible: hasEscapeHandler,
                isHoverable,
                isPersistent: true, // Dropdowns typically persist
                hasEscapeKey: hasEscapeHandler,
                hasCloseButton: false,
                issues,
                severity,
              });
            }
          }
        }
      });

      // Check for popover/modal content
      const popovers = document.querySelectorAll('.popover, .modal, [role="dialog"], [role="tooltip"]');
      popovers.forEach((element, index) => {
        const style = window.getComputedStyle(element);
        const isVisible = style.display !== 'none' && style.visibility !== 'hidden';
        
        if (isVisible) {
          const issues: string[] = [];
          let severity: 'error' | 'warning' | 'info' = 'info';
          
          // Check dismissibility
          const hasCloseButton = element.querySelector('.close, .btn-close, [aria-label*="close"]') !== null;
          const hasEscapeHandler = element.hasAttribute('data-dismiss');
          
          // Check hoverability
          const isHoverable = style.pointerEvents !== 'none';
          
          if (!hasCloseButton && !hasEscapeHandler) {
            issues.push('Popover/modal not dismissible');
            severity = 'error';
          }
          
          if (!isHoverable) {
            issues.push('Popover/modal content not hoverable');
            severity = 'warning';
          }
          
          if (issues.length > 0) {
            problematicElements.push({
              selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
              tagName: element.tagName.toLowerCase(),
              triggerType: 'various',
              contentType: 'popover-modal',
              isDismissible: hasCloseButton || hasEscapeHandler,
              isHoverable,
              isPersistent: true,
              hasEscapeKey: hasEscapeHandler,
              hasCloseButton,
              issues,
              severity,
            });
          }
        }
      });

      // Check CSS for hover/focus content
      const styleSheets = Array.from(document.styleSheets);
      const cssHoverContent: string[] = [];
      
      try {
        styleSheets.forEach(sheet => {
          if (sheet.cssRules) {
            Array.from(sheet.cssRules).forEach(rule => {
              const cssText = rule.cssText || '';
              
              // Look for hover/focus pseudo-classes that show content
              if (/:hover.*display:\s*block/.test(cssText) ||
                  /:focus.*display:\s*block/.test(cssText) ||
                  /:hover.*visibility:\s*visible/.test(cssText) ||
                  /:focus.*visibility:\s*visible/.test(cssText)) {
                cssHoverContent.push('CSS shows content on hover/focus');
              }
            });
          }
        });
      } catch (e) {
        // Cross-origin stylesheets may not be accessible
      }

      return {
        problematicElements,
        cssHoverContent,
        totalHoverElements: hoverElements.length,
        problematicCount: problematicElements.length,
        titleTooltipCount: problematicElements.filter(el => el.contentType === 'title-tooltip').length,
        customTooltipCount: problematicElements.filter(el => el.contentType === 'custom-tooltip').length,
        dropdownCount: problematicElements.filter(el => el.contentType === 'dropdown-menu').length,
        cssHoverCount: cssHoverContent.length,
      };
    });

    let score = 100;
    const elementCount = hoverFocusAnalysis.problematicCount;
    const scanDuration = Date.now() - startTime;

    if (elementCount > 0) {
      // Calculate score based on severity
      hoverFocusAnalysis.problematicElements.forEach((element) => {
        const deduction = element.severity === 'error' ? 12 : 
                         element.severity === 'warning' ? 6 : 3;
        score = Math.max(0, score - deduction);
      });

      issues.push(`${elementCount} hover/focus content elements with issues found`);
      if (hoverFocusAnalysis.titleTooltipCount > 0) {
        issues.push(`${hoverFocusAnalysis.titleTooltipCount} title tooltips don't meet requirements`);
      }
      if (hoverFocusAnalysis.customTooltipCount > 0) {
        issues.push(`${hoverFocusAnalysis.customTooltipCount} custom tooltips have accessibility issues`);
      }

      hoverFocusAnalysis.problematicElements.forEach((element) => {
        evidence.push({
          type: 'code',
          description: `Hover/focus content issue: ${element.issues.join(', ')}`,
          value: `${element.contentType} (${element.triggerType})`,
          selector: element.selector,
          elementCount: 1,
          affectedSelectors: [element.selector],
          severity: element.severity,
          fixExample: {
            before: this.getBeforeExample(element),
            after: this.getAfterExample(element),
            description: this.getFixDescription(element),
            codeExample: this.getCodeExample(element.contentType),
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/content-on-hover-or-focus.html',
              'https://www.w3.org/WAI/WCAG21/Techniques/SCR39',
              'https://www.w3.org/WAI/ARIA/apg/patterns/tooltip/'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              triggerType: element.triggerType,
              contentType: element.contentType,
              isDismissible: element.isDismissible,
              isHoverable: element.isHoverable,
              isPersistent: element.isPersistent,
              hasEscapeKey: element.hasEscapeKey,
              hasCloseButton: element.hasCloseButton,
            },
          },
        });
      });
    }

    // CSS hover content
    if (hoverFocusAnalysis.cssHoverCount > 0) {
      score = Math.max(0, score - (hoverFocusAnalysis.cssHoverCount * 5));
      issues.push(`${hoverFocusAnalysis.cssHoverCount} CSS patterns show content on hover/focus`);
    }

    // Add recommendations
    recommendations.push('Ensure hover/focus content is dismissible (ESC key or close button)');
    recommendations.push('Make hover/focus content hoverable (pointer-events: auto)');
    recommendations.push('Keep hover/focus content persistent until dismissed');
    recommendations.push('Replace title attributes with accessible custom tooltips');
    recommendations.push('Provide keyboard alternatives for hover-only interactions');

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(element: any): string {
    if (element.contentType === 'title-tooltip') {
      return '<button title="Save your changes">Save</button>';
    }
    if (element.contentType === 'custom-tooltip') {
      return '<button aria-describedby="tooltip1">Help</button>\n<div id="tooltip1" style="pointer-events: none;">Tooltip content</div>';
    }
    return `<${element.tagName}>Element with hover content</${element.tagName}>`;
  }

  private getAfterExample(element: any): string {
    if (element.contentType === 'title-tooltip') {
      return '<button aria-describedby="save-tooltip">Save</button>\n<div id="save-tooltip" role="tooltip">Save your changes</div>';
    }
    if (element.contentType === 'custom-tooltip') {
      return '<button aria-describedby="tooltip1">Help</button>\n<div id="tooltip1" role="tooltip" style="pointer-events: auto;">Tooltip content <button aria-label="Close">×</button></div>';
    }
    return `<${element.tagName}>Element with accessible hover content</${element.tagName}>`;
  }

  private getFixDescription(element: any): string {
    if (element.issues.includes('not dismissible')) {
      return 'Add ESC key handler or close button to dismiss content';
    }
    if (element.issues.includes('not hoverable')) {
      return 'Enable pointer events on hover/focus content';
    }
    if (element.contentType === 'title-tooltip') {
      return 'Replace title attribute with accessible custom tooltip';
    }
    return 'Ensure hover/focus content meets dismissible, hoverable, and persistent requirements';
  }

  private getCodeExample(contentType: string): string {
    switch (contentType) {
      case 'title-tooltip':
        return `
<!-- Before: Title attribute (not accessible) -->
<button title="Save your changes to the document">Save</button>

<!-- After: Accessible custom tooltip -->
<button aria-describedby="save-tooltip"
        onmouseenter="showTooltip('save-tooltip')"
        onmouseleave="hideTooltip('save-tooltip')"
        onfocus="showTooltip('save-tooltip')"
        onblur="hideTooltip('save-tooltip')">
  Save
</button>

<div id="save-tooltip" role="tooltip" class="tooltip" hidden>
  Save your changes to the document
  <button class="tooltip-close" onclick="hideTooltip('save-tooltip')" aria-label="Close tooltip">×</button>
</div>

<style>
.tooltip {
  position: absolute;
  background: #333;
  color: white;
  padding: 8px;
  border-radius: 4px;
  pointer-events: auto; /* Hoverable */
  z-index: 1000;
}
</style>

<script>
function showTooltip(id) {
  document.getElementById(id).hidden = false;
}

function hideTooltip(id) {
  document.getElementById(id).hidden = true;
}

// ESC key dismissal
document.addEventListener('keydown', function(e) {
  if (e.key === 'Escape') {
    document.querySelectorAll('.tooltip').forEach(tooltip => {
      tooltip.hidden = true;
    });
  }
});
</script>
        `;
      case 'custom-tooltip':
        return `
<!-- Accessible tooltip implementation -->
<div class="tooltip-container">
  <button aria-describedby="help-tooltip" class="tooltip-trigger">
    Help
  </button>

  <div id="help-tooltip" role="tooltip" class="tooltip" hidden>
    <div class="tooltip-content">
      This field requires a valid email address
    </div>
    <button class="tooltip-close" aria-label="Close tooltip">×</button>
  </div>
</div>

<style>
.tooltip {
  position: absolute;
  background: #333;
  color: white;
  padding: 12px;
  border-radius: 6px;
  max-width: 250px;
  pointer-events: auto; /* Hoverable requirement */
  z-index: 1000;
}

.tooltip-close {
  position: absolute;
  top: 4px;
  right: 4px;
  background: none;
  border: none;
  color: white;
  cursor: pointer;
}
</style>

<script>
class AccessibleTooltip {
  constructor(trigger, tooltip) {
    this.trigger = trigger;
    this.tooltip = tooltip;
    this.isVisible = false;
    this.init();
  }

  init() {
    // Show on hover/focus
    this.trigger.addEventListener('mouseenter', () => this.show());
    this.trigger.addEventListener('focus', () => this.show());

    // Hide on leave/blur (but allow hovering tooltip)
    this.trigger.addEventListener('mouseleave', (e) => {
      if (!this.tooltip.contains(e.relatedTarget)) {
        this.hide();
      }
    });
    this.trigger.addEventListener('blur', () => this.hide());

    // Keep visible when hovering tooltip
    this.tooltip.addEventListener('mouseenter', () => this.show());
    this.tooltip.addEventListener('mouseleave', () => this.hide());

    // Close button
    const closeBtn = this.tooltip.querySelector('.tooltip-close');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.hide());
    }

    // ESC key dismissal
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isVisible) {
        this.hide();
      }
    });
  }

  show() {
    this.tooltip.hidden = false;
    this.isVisible = true;
  }

  hide() {
    this.tooltip.hidden = true;
    this.isVisible = false;
  }
}

// Initialize tooltips
document.querySelectorAll('.tooltip-trigger').forEach(trigger => {
  const tooltipId = trigger.getAttribute('aria-describedby');
  const tooltip = document.getElementById(tooltipId);
  if (tooltip) {
    new AccessibleTooltip(trigger, tooltip);
  }
});
</script>
        `;
      case 'dropdown-menu':
        return `
<!-- Accessible dropdown menu -->
<div class="dropdown">
  <button aria-expanded="false" aria-haspopup="true" class="dropdown-toggle">
    Menu
  </button>

  <ul role="menu" class="dropdown-menu" hidden>
    <li role="menuitem"><a href="#1">Option 1</a></li>
    <li role="menuitem"><a href="#2">Option 2</a></li>
    <li role="menuitem"><a href="#3">Option 3</a></li>
  </ul>
</div>

<script>
class AccessibleDropdown {
  constructor(container) {
    this.container = container;
    this.toggle = container.querySelector('.dropdown-toggle');
    this.menu = container.querySelector('.dropdown-menu');
    this.isOpen = false;
    this.init();
  }

  init() {
    this.toggle.addEventListener('click', () => this.toggleMenu());
    this.toggle.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        this.toggleMenu();
      }
    });

    // ESC key dismissal
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isOpen) {
        this.closeMenu();
      }
    });

    // Click outside to close
    document.addEventListener('click', (e) => {
      if (!this.container.contains(e.target) && this.isOpen) {
        this.closeMenu();
      }
    });
  }

  toggleMenu() {
    this.isOpen ? this.closeMenu() : this.openMenu();
  }

  openMenu() {
    this.menu.hidden = false;
    this.toggle.setAttribute('aria-expanded', 'true');
    this.isOpen = true;
  }

  closeMenu() {
    this.menu.hidden = true;
    this.toggle.setAttribute('aria-expanded', 'false');
    this.isOpen = false;
  }
}

// Initialize dropdowns
document.querySelectorAll('.dropdown').forEach(dropdown => {
  new AccessibleDropdown(dropdown);
});
</script>
        `;
      default:
        return 'Implement accessible hover/focus content with dismissible, hoverable, and persistent requirements';
    }
  }
}

/**
 * WCAG Checks Index
 * Exports all automated WCAG checks
 */

// Import all check classes
import { ContrastMinimumCheck } from './contrast-minimum';
import { FocusVisibleCheck } from './focus-visible';
import { FocusNotObscuredMinimumCheck } from './focus-not-obscured-minimum';
import { FocusNotObscuredEnhancedCheck } from './focus-not-obscured-enhanced';
import { FocusAppearanceCheck } from './focus-appearance';
import { TargetSizeCheck } from './target-size';
import { NonTextContentCheck } from './non-text-content';
import { InfoRelationshipsCheck } from './info-relationships';
import { KeyboardCheck } from './keyboard';
import { ErrorIdentificationCheck } from './error-identification';
import { NameRoleValueCheck } from './name-role-value';
import { RedundantEntryCheck } from './redundant-entry';
import { ImageAlternatives3Check } from './image-alternatives-3';
import { KeyboardFocus3Check } from './keyboard-focus-3';
import { CaptionsCheck } from './captions';
import { FocusOrderCheck } from './focus-order';
import { DraggingMovementsCheck } from './dragging-movements';
import { ConsistentHelpCheck } from './consistent-help';
import { TextWordingCheck } from './text-wording';
import { MotorCheck } from './motor';
import { PronunciationMeaningCheck } from './pronunciation-meaning';
import { AccessibleAuthenticationCheck } from './accessible-authentication';
import { AccessibleAuthenticationEnhancedCheck } from './accessible-authentication-enhanced';
import { HtmlLangCheck } from './html-lang';
import { LandmarksCheck } from './landmarks';
import { LinkPurposeCheck } from './link-purpose';
import { KeyboardTrapCheck } from './keyboard-trap';
import { BypassBlocksCheck } from './bypass-blocks';
import { PageTitledCheck } from './page-titled';
import { LabelsInstructionsCheck } from './labels-instructions';
import { ErrorSuggestionCheck } from './error-suggestion';
import { ErrorPreventionCheck } from './error-prevention';
import { AudioVideoOnlyCheck } from './audio-video-only';
import { AudioDescriptionCheck } from './audio-description';
import { MultipleWaysCheck } from './multiple-ways';
import { HeadingsLabelsCheck } from './headings-labels';
import { LanguagePartsCheck } from './language-parts';
import { TimingAdjustableCheck } from './timing-adjustable';
import { PauseStopHideCheck } from './pause-stop-hide';
import { ThreeFlashesCheck } from './three-flashes';
import { SkipLinksCheck } from './skip-links';
import { EnhancedFocusManagementCheck } from './enhanced-focus-management';
import { LinkContextCheck } from './link-context';
import { ResizeTextCheck } from './resize-text';
import { ImagesOfTextCheck } from './images-of-text';
import { ReflowCheck } from './reflow';
import { NonTextContrastCheck } from './non-text-contrast';
import { TextSpacingCheck } from './text-spacing';
import { ContentOnHoverFocusCheck } from './content-on-hover-focus';
import { AudioControlCheck } from './audio-control';
import { KeyboardAccessibleCheck } from './keyboard-accessible';
import { CharacterKeyShortcutsCheck } from './character-key-shortcuts';
import { PointerGesturesCheck } from './pointer-gestures';
import { PointerCancellationCheck } from './pointer-cancellation';
import { LabelInNameCheck } from './label-in-name';
import { MotionActuationCheck } from './motion-actuation';
import { TargetSizeEnhancedCheck } from './target-size-enhanced';
import { ConcurrentInputMechanismsCheck } from './concurrent-input-mechanisms';
import { UnusualWordsCheck } from './unusual-words';
import { AbbreviationsCheck } from './abbreviations';
import { ReadingLevelCheck } from './reading-level';
import { PronunciationCheck } from './pronunciation';
import { ContextChangesCheck } from './context-changes';
import { HelpCheck } from './help';
import { ErrorPreventionEnhancedCheck } from './error-prevention-enhanced';
import { StatusMessagesCheck } from './status-messages';

// Re-export all check classes
export { ContrastMinimumCheck } from './contrast-minimum';
export { FocusVisibleCheck } from './focus-visible';
export { FocusNotObscuredMinimumCheck } from './focus-not-obscured-minimum';
export { FocusNotObscuredEnhancedCheck } from './focus-not-obscured-enhanced';
export { FocusAppearanceCheck } from './focus-appearance';
export { TargetSizeCheck } from './target-size';
export { NonTextContentCheck } from './non-text-content';
export { InfoRelationshipsCheck } from './info-relationships';
export { KeyboardCheck } from './keyboard';
export { ErrorIdentificationCheck } from './error-identification';
export { NameRoleValueCheck } from './name-role-value';
export { RedundantEntryCheck } from './redundant-entry';
export { ImageAlternatives3Check } from './image-alternatives-3';
export { KeyboardFocus3Check } from './keyboard-focus-3';
export { CaptionsCheck } from './captions';
export { FocusOrderCheck } from './focus-order';
export { DraggingMovementsCheck } from './dragging-movements';
export { ConsistentHelpCheck } from './consistent-help';
export { TextWordingCheck } from './text-wording';
export { MotorCheck } from './motor';
export { PronunciationMeaningCheck } from './pronunciation-meaning';
export { AccessibleAuthenticationCheck } from './accessible-authentication';
export { AccessibleAuthenticationEnhancedCheck } from './accessible-authentication-enhanced';
export { HtmlLangCheck } from './html-lang';
export { LandmarksCheck } from './landmarks';
export { LinkPurposeCheck } from './link-purpose';
export { KeyboardTrapCheck } from './keyboard-trap';
export { BypassBlocksCheck } from './bypass-blocks';
export { PageTitledCheck } from './page-titled';
export { LabelsInstructionsCheck } from './labels-instructions';
export { ErrorSuggestionCheck } from './error-suggestion';
export { ErrorPreventionCheck } from './error-prevention';
export { AudioVideoOnlyCheck } from './audio-video-only';
export { AudioDescriptionCheck } from './audio-description';
export { MultipleWaysCheck } from './multiple-ways';
export { HeadingsLabelsCheck } from './headings-labels';
export { LanguagePartsCheck } from './language-parts';
export { TimingAdjustableCheck } from './timing-adjustable';
export { PauseStopHideCheck } from './pause-stop-hide';
export { ThreeFlashesCheck } from './three-flashes';
export { SkipLinksCheck } from './skip-links';
export { EnhancedFocusManagementCheck } from './enhanced-focus-management';
export { LinkContextCheck } from './link-context';
export { ResizeTextCheck } from './resize-text';
export { ImagesOfTextCheck } from './images-of-text';
export { ReflowCheck } from './reflow';
export { NonTextContrastCheck } from './non-text-contrast';
export { TextSpacingCheck } from './text-spacing';
export { ContentOnHoverFocusCheck } from './content-on-hover-focus';
export { AudioControlCheck } from './audio-control';
export { KeyboardAccessibleCheck } from './keyboard-accessible';
export { CharacterKeyShortcutsCheck } from './character-key-shortcuts';
export { PointerGesturesCheck } from './pointer-gestures';
export { PointerCancellationCheck } from './pointer-cancellation';
export { LabelInNameCheck } from './label-in-name';
export { MotionActuationCheck } from './motion-actuation';
export { TargetSizeEnhancedCheck } from './target-size-enhanced';
export { ConcurrentInputMechanismsCheck } from './concurrent-input-mechanisms';
export { UnusualWordsCheck } from './unusual-words';
export { AbbreviationsCheck } from './abbreviations';
export { ReadingLevelCheck } from './reading-level';
export { PronunciationCheck } from './pronunciation';
export { ContextChangesCheck } from './context-changes';
export { HelpCheck } from './help';
export { ErrorPreventionEnhancedCheck } from './error-prevention-enhanced';
export { StatusMessagesCheck } from './status-messages';

// Check registry for easy access
export const AUTOMATED_CHECKS = {
  // Part 3: Fully Automated (100%)
  'WCAG-004': 'ContrastMinimumCheck',
  'WCAG-007': 'FocusVisibleCheck',
  'WCAG-010': 'FocusNotObscuredMinimumCheck',
  'WCAG-011': 'FocusNotObscuredEnhancedCheck',
  'WCAG-012': 'FocusAppearanceCheck',
  'WCAG-014': 'TargetSizeCheck',

  // Part 4: Very High Automation (85-95%)
  'WCAG-001': 'NonTextContentCheck',
  'WCAG-003': 'InfoRelationshipsCheck',
  'WCAG-005': 'KeyboardCheck',
  'WCAG-008': 'ErrorIdentificationCheck',
  'WCAG-009': 'NameRoleValueCheck',
  'WCAG-016': 'RedundantEntryCheck',
  'WCAG-017': 'ImageAlternatives3Check',
  'WCAG-019': 'KeyboardFocus3Check',

  // Part 5: High & Medium Automation (60-80%)
  'WCAG-002': 'CaptionsCheck',
  'WCAG-006': 'FocusOrderCheck',
  'WCAG-013': 'DraggingMovementsCheck',
  'WCAG-015': 'ConsistentHelpCheck',
  'WCAG-018': 'TextWordingCheck',
  'WCAG-020': 'MotorCheck',
  'WCAG-021': 'PronunciationMeaningCheck',

  // Enhanced Checks (New Implementation)
  'WCAG-024': 'HtmlLangCheck',
  'WCAG-025': 'LandmarksCheck',
  'WCAG-026': 'LinkPurposeCheck',
  'WCAG-027': 'KeyboardTrapCheck',
  'WCAG-028': 'BypassBlocksCheck',
  'WCAG-029': 'PageTitledCheck',
  'WCAG-030': 'LabelsInstructionsCheck',
  'WCAG-031': 'ErrorSuggestionCheck',
  'WCAG-032': 'ErrorPreventionCheck',
  'WCAG-033': 'AudioVideoOnlyCheck',
  'WCAG-034': 'AudioDescriptionCheck',
  'WCAG-035': 'MultipleWaysCheck',
  'WCAG-036': 'HeadingsLabelsCheck',
  'WCAG-038': 'LanguagePartsCheck',
  'WCAG-044': 'TimingAdjustableCheck',
  'WCAG-045': 'PauseStopHideCheck',
  'WCAG-046': 'ThreeFlashesCheck',
  'WCAG-047': 'SkipLinksCheck',
  'WCAG-048': 'EnhancedFocusManagementCheck',
  'WCAG-049': 'LinkContextCheck',
  'WCAG-037': 'ResizeTextCheck',
  'WCAG-039': 'ImagesOfTextCheck',
  'WCAG-040': 'ReflowCheck',
  'WCAG-041': 'NonTextContrastCheck',
  'WCAG-042': 'TextSpacingCheck',
  'WCAG-043': 'ContentOnHoverFocusCheck',
  'WCAG-050': 'AudioControlCheck',
  'WCAG-051': 'KeyboardAccessibleCheck',
  'WCAG-052': 'CharacterKeyShortcutsCheck',
  'WCAG-053': 'PointerGesturesCheck',
  'WCAG-054': 'PointerCancellationCheck',
  'WCAG-055': 'LabelInNameCheck',
  'WCAG-056': 'MotionActuationCheck',
  'WCAG-058': 'TargetSizeEnhancedCheck',
  'WCAG-059': 'ConcurrentInputMechanismsCheck',
  'WCAG-060': 'UnusualWordsCheck',
  'WCAG-061': 'AbbreviationsCheck',
  'WCAG-062': 'ReadingLevelCheck',
  'WCAG-063': 'PronunciationCheck',
  'WCAG-064': 'ContextChangesCheck',
  'WCAG-065': 'HelpCheck',
  'WCAG-066': 'ErrorPreventionEnhancedCheck',
  'WCAG-057': 'StatusMessagesCheck',
} as const;

/**
 * Get check implementation by rule ID
 */
export function getCheckImplementation(ruleId: string) {
  // Dynamic import approach to avoid circular dependencies
  switch (ruleId) {
    // Part 3: Fully Automated (100%)
    case 'WCAG-004':
      return ContrastMinimumCheck;
    case 'WCAG-007':
      return FocusVisibleCheck;
    case 'WCAG-010':
      return FocusNotObscuredMinimumCheck;
    case 'WCAG-011':
      return FocusNotObscuredEnhancedCheck;
    case 'WCAG-012':
      return FocusAppearanceCheck;
    case 'WCAG-014':
      return TargetSizeCheck;

    // Part 4: Very High Automation (85-95%)
    case 'WCAG-001':
      return NonTextContentCheck;
    case 'WCAG-003':
      return InfoRelationshipsCheck;
    case 'WCAG-005':
      return KeyboardCheck;
    case 'WCAG-008':
      return ErrorIdentificationCheck;
    case 'WCAG-009':
      return NameRoleValueCheck;
    case 'WCAG-016':
      return RedundantEntryCheck;
    case 'WCAG-017':
      return ImageAlternatives3Check;
    case 'WCAG-019':
      return KeyboardFocus3Check;

    // Part 5: High & Medium Automation (60-80%)
    case 'WCAG-002':
      return CaptionsCheck;
    case 'WCAG-006':
      return FocusOrderCheck;
    case 'WCAG-013':
      return DraggingMovementsCheck;
    case 'WCAG-015':
      return ConsistentHelpCheck;
    case 'WCAG-018':
      return TextWordingCheck;
    case 'WCAG-020':
      return MotorCheck;
    case 'WCAG-021':
      return PronunciationMeaningCheck;
    case 'WCAG-022':
      return AccessibleAuthenticationCheck;
    case 'WCAG-023':
      return AccessibleAuthenticationEnhancedCheck;
    case 'WCAG-024':
      return HtmlLangCheck;
    case 'WCAG-025':
      return LandmarksCheck;
    case 'WCAG-026':
      return LinkPurposeCheck;
    case 'WCAG-027':
      return KeyboardTrapCheck;
    case 'WCAG-028':
      return BypassBlocksCheck;
    case 'WCAG-029':
      return PageTitledCheck;
    case 'WCAG-030':
      return LabelsInstructionsCheck;
    case 'WCAG-031':
      return ErrorSuggestionCheck;
    case 'WCAG-032':
      return ErrorPreventionCheck;
    case 'WCAG-033':
      return AudioVideoOnlyCheck;
    case 'WCAG-034':
      return AudioDescriptionCheck;
    case 'WCAG-035':
      return MultipleWaysCheck;
    case 'WCAG-036':
      return HeadingsLabelsCheck;
    case 'WCAG-038':
      return LanguagePartsCheck;
    case 'WCAG-044':
      return TimingAdjustableCheck;
    case 'WCAG-045':
      return PauseStopHideCheck;
    case 'WCAG-046':
      return ThreeFlashesCheck;
    case 'WCAG-047':
      return SkipLinksCheck;
    case 'WCAG-048':
      return EnhancedFocusManagementCheck;
    case 'WCAG-049':
      return LinkContextCheck;
    case 'WCAG-037':
      return ResizeTextCheck;
    case 'WCAG-039':
      return ImagesOfTextCheck;
    case 'WCAG-040':
      return ReflowCheck;
    case 'WCAG-041':
      return NonTextContrastCheck;
    case 'WCAG-042':
      return TextSpacingCheck;
    case 'WCAG-043':
      return ContentOnHoverFocusCheck;
    case 'WCAG-050':
      return AudioControlCheck;
    case 'WCAG-051':
      return KeyboardAccessibleCheck;
    case 'WCAG-052':
      return CharacterKeyShortcutsCheck;
    case 'WCAG-053':
      return PointerGesturesCheck;
    case 'WCAG-054':
      return PointerCancellationCheck;
    case 'WCAG-055':
      return LabelInNameCheck;
    case 'WCAG-056':
      return MotionActuationCheck;
    case 'WCAG-058':
      return TargetSizeEnhancedCheck;
    case 'WCAG-059':
      return ConcurrentInputMechanismsCheck;
    case 'WCAG-060':
      return UnusualWordsCheck;
    case 'WCAG-061':
      return AbbreviationsCheck;
    case 'WCAG-062':
      return ReadingLevelCheck;
    case 'WCAG-063':
      return PronunciationCheck;
    case 'WCAG-064':
      return ContextChangesCheck;
    case 'WCAG-065':
      return HelpCheck;
    case 'WCAG-066':
      return ErrorPreventionEnhancedCheck;
    case 'WCAG-057':
      return StatusMessagesCheck;

    default:
      return undefined;
  }
}

/**
 * Get automation level for a rule
 */
export function getAutomationLevel(ruleId: string): number {
  const automationLevels: Record<string, number> = {
    'WCAG-001': 0.95,
    'WCAG-002': 0.8,
    'WCAG-003': 0.9,
    'WCAG-004': 1.0,
    'WCAG-005': 0.85,
    'WCAG-006': 0.75,
    'WCAG-007': 1.0,
    'WCAG-008': 0.9,
    'WCAG-009': 0.9,
    'WCAG-010': 1.0,
    'WCAG-011': 1.0,
    'WCAG-012': 1.0,
    'WCAG-013': 0.7,
    'WCAG-014': 1.0,
    'WCAG-015': 0.8,
    'WCAG-016': 0.85,
    'WCAG-017': 0.95,
    'WCAG-018': 0.75,
    'WCAG-019': 0.9,
    'WCAG-020': 0.8,
    'WCAG-021': 0.6,
    'WCAG-022': 0.5,
    'WCAG-023': 0.4,
    'WCAG-024': 1.0,  // HTML Language Check - fully automated
    'WCAG-025': 0.95, // Landmarks Check - very high automation
    'WCAG-026': 0.90, // Link Purpose Check - high automation
    'WCAG-027': 0.85, // Keyboard Trap Check - high automation
    'WCAG-028': 0.9, // Bypass Blocks Check - very high automation
    'WCAG-029': 1.0, // Page Titled Check - fully automated
    'WCAG-030': 0.8, // Labels or Instructions Check - high automation
    'WCAG-031': 0.75, // Error Suggestion Check - high automation
    'WCAG-032': 0.7, // Error Prevention Check - medium-high automation
    'WCAG-033': 0.65, // Audio-only and Video-only Check - medium automation
    'WCAG-034': 0.6, // Audio Description Check - medium automation
    'WCAG-035': 0.7, // Multiple Ways Check - medium-high automation
    'WCAG-036': 0.8, // Headings and Labels Check - high automation
    'WCAG-038': 0.65, // Language of Parts Check - medium automation
    'WCAG-044': 0.85, // Timing Adjustable Check - high automation
    'WCAG-045': 0.8, // Pause, Stop, Hide Check - high automation
    'WCAG-046': 0.75, // Three Flashes Check - medium-high automation
    'WCAG-047': 0.9, // Skip Links Check - very high automation
    'WCAG-048': 0.85, // Enhanced Focus Management Check - high automation
    'WCAG-049': 0.8, // Link Context Check - high automation
    'WCAG-037': 0.75, // Resize Text Check - medium-high automation
    'WCAG-039': 0.7, // Images of Text Check - medium-high automation
    'WCAG-040': 0.8, // Reflow Check - high automation
    'WCAG-041': 0.75, // Non-text Contrast Check - medium-high automation
    'WCAG-042': 0.85, // Text Spacing Check - high automation
    'WCAG-043': 0.75, // Content on Hover/Focus Check - medium-high automation
    'WCAG-050': 0.8, // Audio Control Check - high automation
    'WCAG-051': 0.85, // Keyboard Accessible Check - high automation
    'WCAG-052': 0.7, // Character Key Shortcuts Check - medium-high automation
    'WCAG-053': 0.75, // Pointer Gestures Check - medium-high automation
    'WCAG-054': 0.8, // Pointer Cancellation Check - high automation
    'WCAG-055': 0.85, // Label in Name Check - high automation
    'WCAG-056': 0.75, // Motion Actuation Check - medium-high automation
    'WCAG-058': 0.7,  // Target Size Enhanced Check - medium-high automation
    'WCAG-059': 0.65, // Concurrent Input Mechanisms Check - medium automation
    'WCAG-060': 0.6,  // Unusual Words Check - medium automation
    'WCAG-061': 0.75, // Abbreviations Check - medium-high automation
    'WCAG-062': 0.55, // Reading Level Check - medium automation
    'WCAG-063': 0.5,  // Pronunciation Check - medium automation
    'WCAG-064': 0.7,  // Context Changes Check - medium-high automation
    'WCAG-065': 0.8,  // Help Check - high automation
    'WCAG-066': 0.85, // Error Prevention Enhanced Check - high automation
    'WCAG-057': 0.8,  // Status Messages Check - high automation
  };
  return automationLevels[ruleId] || 0;
}

export type AutomatedCheckType = keyof typeof AUTOMATED_CHECKS;

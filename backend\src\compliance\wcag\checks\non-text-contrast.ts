/**
 * WCAG-041: Non-text Contrast Check
 * Success Criterion: 1.4.11 Non-text Contrast (Level AA)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckTemplate, CheckConfig } from '../utils/check-template';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceProcessor } from '../utils/evidence-processor';

export class NonTextContrastCheck {
  private checkTemplate = new CheckTemplate();

  async performCheck(config: CheckConfig): Promise<WcagCheckResultEnhanced> {
    const result = await this.checkTemplate.executeCheck(
      'WCAG-041',
      'Non-text Contrast',
      'perceivable',
      0.0535,
      'AA',
      config,
      this.executeNonTextContrastCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Convert to enhanced result
    return {
      ...result,
      evidence: EvidenceProcessor.processEvidence(result.evidence),
    };
  }

  private async executeNonTextContrastCheck(
    page: Page,
    _config: CheckConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze non-text elements for contrast
    const contrastAnalysis = await page.evaluate(() => {
      // Helper function to convert color to RGB
      function parseColor(color: string): { r: number; g: number; b: number } | null {
        if (!color || color === 'transparent' || color === 'none') return null;
        
        // Handle rgb/rgba
        const rgbMatch = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/);
        if (rgbMatch) {
          return {
            r: parseInt(rgbMatch[1]),
            g: parseInt(rgbMatch[2]),
            b: parseInt(rgbMatch[3])
          };
        }
        
        // Handle hex colors
        const hexMatch = color.match(/^#([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i);
        if (hexMatch) {
          return {
            r: parseInt(hexMatch[1], 16),
            g: parseInt(hexMatch[2], 16),
            b: parseInt(hexMatch[3], 16)
          };
        }
        
        return null;
      }

      // Calculate relative luminance
      function getLuminance(color: { r: number; g: number; b: number }): number {
        const { r, g, b } = color;
        const [rs, gs, bs] = [r, g, b].map(c => {
          c = c / 255;
          return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
        });
        return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
      }

      // Calculate contrast ratio
      function getContrastRatio(color1: { r: number; g: number; b: number }, color2: { r: number; g: number; b: number }): number {
        const lum1 = getLuminance(color1);
        const lum2 = getLuminance(color2);
        const brightest = Math.max(lum1, lum2);
        const darkest = Math.min(lum1, lum2);
        return (brightest + 0.05) / (darkest + 0.05);
      }

      const problematicElements: Array<{
        selector: string;
        tagName: string;
        elementType: string;
        foregroundColor: string;
        backgroundColor: string;
        borderColor: string;
        contrastRatio: number;
        requiredRatio: number;
        passes: boolean;
        issues: string[];
        severity: 'error' | 'warning' | 'info';
        isInteractive: boolean;
        hasState: boolean;
      }> = [];

      // Get all interactive and UI elements
      const uiElements = document.querySelectorAll(`
        button, input, select, textarea, a[href], 
        [role="button"], [role="link"], [role="menuitem"], [role="tab"], 
        [role="checkbox"], [role="radio"], [role="slider"], [role="spinbutton"],
        .btn, .button, .link, .nav-item, .menu-item, .form-control,
        [tabindex]:not([tabindex="-1"])
      `);

      uiElements.forEach((element, index) => {
        const computedStyle = window.getComputedStyle(element);
        const rect = element.getBoundingClientRect();
        
        // Skip very small or hidden elements
        if (rect.width < 10 || rect.height < 10) return;
        
        const tagName = element.tagName.toLowerCase();
        const isInteractive = ['button', 'input', 'select', 'textarea', 'a'].includes(tagName) ||
                             element.hasAttribute('role') ||
                             element.hasAttribute('tabindex');
        
        // Get colors
        const foregroundColor = computedStyle.color;
        const backgroundColor = computedStyle.backgroundColor;
        const borderColor = computedStyle.borderColor || computedStyle.borderTopColor;
        
        // Parse colors
        const fgColor = parseColor(foregroundColor);
        const bgColor = parseColor(backgroundColor);
        const bdColor = parseColor(borderColor);
        
        const issues: string[] = [];
        let severity: 'error' | 'warning' | 'info' = 'info';
        let contrastRatio = 0;
        let passes = true;
        
        // Required contrast ratio for UI components is 3:1
        const requiredRatio = 3.0;
        
        // Check border contrast (for form controls, buttons)
        if (bdColor && bgColor) {
          contrastRatio = getContrastRatio(bdColor, bgColor);
          if (contrastRatio < requiredRatio) {
            issues.push(`Border contrast ${contrastRatio.toFixed(2)}:1 below required ${requiredRatio}:1`);
            passes = false;
            severity = 'error';
          }
        }
        
        // Check background contrast with parent
        const parent = element.parentElement;
        if (parent && bgColor) {
          const parentStyle = window.getComputedStyle(parent);
          const parentBgColor = parseColor(parentStyle.backgroundColor);
          if (parentBgColor) {
            const bgContrastRatio = getContrastRatio(bgColor, parentBgColor);
            if (bgContrastRatio < requiredRatio) {
              issues.push(`Background contrast ${bgContrastRatio.toFixed(2)}:1 below required ${requiredRatio}:1`);
              passes = false;
              severity = 'error';
            }
          }
        }
        
        // Check for focus indicators
        const outline = computedStyle.outline;
        const outlineColor = computedStyle.outlineColor;
        const boxShadow = computedStyle.boxShadow;
        
        if (isInteractive) {
          const hasFocusIndicator = outline !== 'none' || 
                                   outlineColor !== 'transparent' ||
                                   boxShadow !== 'none';
          
          if (!hasFocusIndicator) {
            issues.push('Missing visible focus indicator');
            severity = severity === 'error' ? 'error' : 'warning';
          }
        }
        
        // Check for state indicators (hover, active, disabled)
        const hasState = element.matches(':hover, :active, :focus, :disabled') ||
                         element.hasAttribute('aria-pressed') ||
                         element.hasAttribute('aria-selected') ||
                         element.hasAttribute('aria-expanded');
        
        // Determine element type
        let elementType = 'ui-component';
        if (tagName === 'button' || element.getAttribute('role') === 'button') {
          elementType = 'button';
        } else if (tagName === 'input') {
          elementType = 'form-control';
        } else if (tagName === 'a') {
          elementType = 'link';
        }
        
        if (!passes || issues.length > 0) {
          problematicElements.push({
            selector: `${tagName}:nth-of-type(${index + 1})`,
            tagName,
            elementType,
            foregroundColor,
            backgroundColor,
            borderColor,
            contrastRatio,
            requiredRatio,
            passes,
            issues,
            severity,
            isInteractive,
            hasState,
          });
        }
      });

      // Check for custom graphics and icons
      const graphicElements = document.querySelectorAll('svg, canvas, img[src*="icon"], .icon, [class*="icon"]');
      
      graphicElements.forEach((element, index) => {
        const computedStyle = window.getComputedStyle(element);
        const rect = element.getBoundingClientRect();
        
        if (rect.width < 10 || rect.height < 10) return;
        
        const fill = computedStyle.fill || element.getAttribute('fill');
        const stroke = computedStyle.stroke || element.getAttribute('stroke');
        const backgroundColor = computedStyle.backgroundColor;
        
        const issues: string[] = [];
        
        // For SVG icons, check if they have sufficient contrast
        if (element.tagName === 'SVG') {
          const fillColor = parseColor(fill || '');
          const bgColor = parseColor(backgroundColor);
          
          if (fillColor && bgColor) {
            const contrastRatio = getContrastRatio(fillColor, bgColor);
            if (contrastRatio < 3.0) {
              issues.push(`Icon contrast ${contrastRatio.toFixed(2)}:1 below required 3:1`);
              
              problematicElements.push({
                selector: `svg:nth-of-type(${index + 1})`,
                tagName: 'svg',
                elementType: 'icon',
                foregroundColor: fill || '',
                backgroundColor,
                borderColor: stroke || '',
                contrastRatio,
                requiredRatio: 3.0,
                passes: false,
                issues,
                severity: 'error',
                isInteractive: false,
                hasState: false,
              });
            }
          }
        }
      });

      return {
        problematicElements,
        totalUIElements: uiElements.length,
        totalGraphicElements: graphicElements.length,
        problematicCount: problematicElements.length,
        errorCount: problematicElements.filter(el => el.severity === 'error').length,
        warningCount: problematicElements.filter(el => el.severity === 'warning').length,
        interactiveCount: problematicElements.filter(el => el.isInteractive).length,
      };
    });

    let score = 100;
    const elementCount = contrastAnalysis.problematicCount;
    const scanDuration = Date.now() - startTime;

    if (elementCount > 0) {
      // Calculate score based on severity
      contrastAnalysis.problematicElements.forEach((element) => {
        const deduction = element.severity === 'error' ? 12 : 
                         element.severity === 'warning' ? 6 : 3;
        score = Math.max(0, score - deduction);
      });

      issues.push(`${elementCount} UI elements with contrast issues found`);
      if (contrastAnalysis.errorCount > 0) {
        issues.push(`${contrastAnalysis.errorCount} elements fail 3:1 contrast requirement`);
      }
      if (contrastAnalysis.interactiveCount > 0) {
        issues.push(`${contrastAnalysis.interactiveCount} interactive elements have contrast issues`);
      }

      contrastAnalysis.problematicElements.forEach((element) => {
        evidence.push({
          type: 'code',
          description: `Non-text contrast issue: ${element.issues.join(', ')}`,
          value: `${element.elementType}: ${element.contrastRatio.toFixed(2)}:1 (required: ${element.requiredRatio}:1)`,
          selector: element.selector,
          elementCount: 1,
          affectedSelectors: [element.selector],
          severity: element.severity,
          fixExample: {
            before: this.getBeforeExample(element),
            after: this.getAfterExample(element),
            description: this.getFixDescription(element),
            codeExample: this.getCodeExample(element.elementType),
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/non-text-contrast.html',
              'https://www.w3.org/WAI/WCAG21/Techniques/G195',
              'https://www.w3.org/WAI/WCAG21/Techniques/G174'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              elementType: element.elementType,
              contrastRatio: element.contrastRatio,
              requiredRatio: element.requiredRatio,
              passes: element.passes,
              isInteractive: element.isInteractive,
              foregroundColor: element.foregroundColor,
              backgroundColor: element.backgroundColor,
              borderColor: element.borderColor,
            },
          },
        });
      });
    }

    // Add recommendations
    recommendations.push('Ensure UI components have 3:1 contrast ratio with adjacent colors');
    recommendations.push('Use sufficient contrast for borders, focus indicators, and state changes');
    recommendations.push('Test interactive elements in all states (default, hover, focus, active)');
    recommendations.push('Provide visible focus indicators for all interactive elements');
    recommendations.push('Use color contrast tools to verify non-text element contrast');

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(element: any): string {
    if (element.elementType === 'button') {
      return '<button style="background: #f0f0f0; border: 1px solid #e0e0e0;">Button</button>';
    }
    if (element.elementType === 'form-control') {
      return '<input type="text" style="border: 1px solid #ddd; background: #fafafa;">';
    }
    if (element.elementType === 'icon') {
      return '<svg fill="#ccc"><path d="..."/></svg>';
    }
    return `<${element.tagName} style="background: ${element.backgroundColor}; border: 1px solid ${element.borderColor};">Element</${element.tagName}>`;
  }

  private getAfterExample(element: any): string {
    if (element.elementType === 'button') {
      return '<button style="background: #007cba; border: 1px solid #005a87; color: white;">Button</button>';
    }
    if (element.elementType === 'form-control') {
      return '<input type="text" style="border: 2px solid #666; background: white;">';
    }
    if (element.elementType === 'icon') {
      return '<svg fill="#333"><path d="..."/></svg>';
    }
    return `<${element.tagName} style="background: #fff; border: 2px solid #333;">Element</${element.tagName}>`;
  }

  private getFixDescription(element: any): string {
    if (element.issues.includes('Border contrast')) {
      return 'Increase border contrast to meet 3:1 ratio requirement';
    }
    if (element.issues.includes('Background contrast')) {
      return 'Increase background contrast with surrounding elements';
    }
    if (element.issues.includes('Missing visible focus indicator')) {
      return 'Add visible focus indicator with sufficient contrast';
    }
    if (element.issues.includes('Icon contrast')) {
      return 'Increase icon color contrast to meet 3:1 ratio';
    }
    return 'Ensure UI component meets 3:1 contrast ratio requirement';
  }

  private getCodeExample(elementType: string): string {
    switch (elementType) {
      case 'button':
        return `
/* Before: Insufficient contrast */
.button {
  background-color: #f0f0f0;
  border: 1px solid #e0e0e0;
  color: #999;
}

/* After: Sufficient contrast (3:1 minimum) */
.button {
  background-color: #007cba;
  border: 2px solid #005a87;
  color: white;
}

.button:focus {
  outline: 2px solid #ff6b35;
  outline-offset: 2px;
}

.button:hover {
  background-color: #005a87;
  border-color: #003d5c;
}
        `;
      case 'form-control':
        return `
/* Before: Insufficient border contrast */
.form-control {
  border: 1px solid #ddd;
  background: #fafafa;
}

/* After: Sufficient contrast */
.form-control {
  border: 2px solid #666;
  background: white;
}

.form-control:focus {
  border-color: #007cba;
  outline: 2px solid #ff6b35;
  outline-offset: 1px;
}

.form-control:invalid {
  border-color: #d32f2f;
}
        `;
      case 'icon':
        return `
/* Before: Low contrast icon */
.icon {
  fill: #ccc;
  stroke: none;
}

/* After: High contrast icon */
.icon {
  fill: #333;
  stroke: none;
}

/* For interactive icons */
.icon-button:focus .icon {
  fill: #007cba;
}

.icon-button:hover .icon {
  fill: #005a87;
}

/* Ensure sufficient contrast in all states */
@media (prefers-color-scheme: dark) {
  .icon {
    fill: #fff;
  }
}
        `;
      default:
        return `
/* General UI component contrast guidelines */
.ui-component {
  /* Ensure 3:1 contrast ratio minimum */
  border: 2px solid #666;
  background: white;
}

/* Focus indicators must be visible */
.ui-component:focus {
  outline: 2px solid #007cba;
  outline-offset: 2px;
}

/* State changes should be clearly visible */
.ui-component:hover {
  border-color: #333;
}

.ui-component:active {
  background: #f0f0f0;
}

.ui-component:disabled {
  opacity: 0.6;
  border-color: #999;
}
        `;
    }
  }
}
